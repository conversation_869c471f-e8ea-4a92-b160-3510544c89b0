import React, { useEffect, useState, useCallback } from "react";
import DeviceTestContent from "./DeviceTestContent";
import DeviceTestingSection from "../components/DeviceTestingSection";
import TermsAndConditionsSection from "../components/TermsAndConditionsSection";
import InterviewSection from "../components/InterviewSection/InterviewSection";
import EndInterviewSection from "../components/EndInterviewSection";
import { useMediaStreams } from "../hooks/useMediaStreams";
import { useRecording } from "../hooks/useRecording";
import { useQuestions } from "../hooks/useQuestions";
import { useInterviewState } from "../hooks/useInterviewState";
import Loader from "../../../components/Loader";
import { useNavigate, useParams } from "react-router-dom";
import ReviewVideoProfile from "../components/ReviewVideoProfile";
import toast from "react-hot-toast";
import { HiOutlineSparkles } from "react-icons/hi";
import { uploadVideo, validateVideoFile, getUploadMethodInfo } from "../../../utils/videoUploadService";

export default function DeviceTest() {
  const navigate = useNavigate();
  const [currentSection, setCurrentSection] = useState("loading");
  const [isLoading, setIsLoading] = useState(false);
  const [loadingText, setLoadingText] = useState("Loading...");
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadMethod, setUploadMethod] = useState(null);
  const [termsAcceptedAndDismissed, setTermsAcceptedAndDismissed] = useState(false);
  const { vpid } = useParams();
  const { localCamStream, startWebcam, stopAllStreams } = useMediaStreams();
  const { startRecording, stopRecording, recordedChunks, addPollyAudioToMix } =
    useRecording(localCamStream);
  const { isInterviewActive, startInterview, endInterview, resetInterview } =
    useInterviewState();

  const {
    questions,
    currentQuestion,
    currentIndex,
    nextQuestion,
    startAnswering,
    updateAnswer,
    initializeQuestions,
    loadQuestionsData,
    activateQuestions,
    questionsLoaded,
    error,
    isLoading: questionsLoading,
    cleanup, // Phase 1: Removed requirement status properties to simplify interview flow
    setEndInterviewCallback // New function to set the callback for AI termination
  } = useQuestions(isInterviewActive, vpid);

  const [score, setScore] = useState({
    score: {
      Communication_Score: 0,
      Skill_Score: 0,
      Overall_Score: 0,
    },
    Suggestions: "",
  });
  const [videoUrl, setVideoUrl] = useState("");
  const [status, setStatus] = useState("inactive");

  // --- All handlers and effects from original file ---
  const handleStartInterview = async () => {
    // Dismiss terms if not already done
    setTermsAcceptedAndDismissed(true);

    // Ensure questions are loaded
    if (!questionsLoaded || !questions || questions.length === 0) {
      await loadQuestionsData();
      await new Promise((resolve) => setTimeout(resolve, 500));
      if (!questionsLoaded || !questions || questions.length === 0) {
        toast.error(
          "Failed to load interview questions. Please refresh the page."
        );
        return;
      }
    }
    const activated = activateQuestions();
    if (!activated) {
      toast.error("Failed to start interview - questions not ready");
      return;
    }
    startInterview();
    setCurrentSection("interview");
    startRecording();
    handleFullScreen();
  };

  const handleFullScreen = async () => {
    try {
      const element = document.documentElement;
      if (element.requestFullscreen) {
        await element.requestFullscreen();
      } else if (element.mozRequestFullScreen) {
        await element.mozRequestFullScreen();
      } else if (element.webkitRequestFullscreen) {
        await element.webkitRequestFullscreen();
      } else if (element.msRequestFullscreen) {
        await element.msRequestFullscreen();
      }
    } catch (error) {
      // Not critical
    }
  };

  const handleAcceptTerms = async () => {
    try {
      if (!questionsLoaded || !questions || questions.length === 0) {
        await loadQuestionsData();
        await new Promise((resolve) => setTimeout(resolve, 500));
        if (!questionsLoaded || !questions || questions.length === 0) {
          toast.error(
            "Failed to load interview questions. Please refresh the page."
          );
          return;
        }
      }
      const activated = activateQuestions();
      if (!activated) {
        toast.error("Failed to start interview - questions not ready");
        return;
      }
      startInterview();
      setCurrentSection("interview");
      startRecording();
      handleFullScreen();
    } catch (error) {
      toast.error(`Failed to start interview: ${error.message}`);
    }
  };

  const exitFullScreen = async () => {
    try {
      const isFullScreen =
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.mozFullScreenElement ||
        document.msFullscreenElement;
      if (!isFullScreen) return;
      if (document.exitFullscreen) {
        await document.exitFullscreen();
      } else if (document.mozCancelFullScreen) {
        await document.mozCancelFullScreen();
      } else if (document.webkitExitFullscreen) {
        await document.webkitExitFullscreen();
      } else if (document.msExitFullscreen) {
        await document.msExitFullscreen();
      }
    } catch (error) {}
  };

  const handleEndInterview = useCallback(async () => {
    // Cleanup timers and API calls from useQuestions
    if (typeof cleanup === "function") {
      cleanup();
    }

    // Ensure the last answer is saved before finalizing the interview
    if (typeof updateAnswer === "function" && currentQuestion && typeof currentIndex === "number") {
      await updateAnswer(currentIndex, currentQuestion.answer ?? "");
    }

    let scoreData = null;
    let uploadSuccess = false;
    let fetchedQuestions = []; // Declare fetchedQuestions here

    try {
      setLoadingText("Preparing interview data...");
      setIsLoading(true);
      setCurrentSection("loading");

      // Stop all active processes
      window.speechSynthesis.cancel();
      endInterview();
      // Stop recording and wait for completion
      await stopRecording();

      // Wait a bit for final chunks to be processed
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Check if we have video data
      if (!recordedChunks || recordedChunks.length === 0) {
        throw new Error("No video data was recorded");
      }

      stopAllStreams();
      exitFullScreen();

      // Fetch the latest questions data from the backend
      setLoadingText("Fetching latest interview data...");
      const videoProfileResponse = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/video-resume-data/${vpid}`,
        {
          method: "GET",
        }
      );

      if (!videoProfileResponse.ok) {
        const errorText = await videoProfileResponse.text();
        throw new Error(
          `Failed to fetch latest video profile data: ${videoProfileResponse.status} ${videoProfileResponse.statusText}. Details: ${errorText}`
        );
      }

      const videoProfileData = await videoProfileResponse.json();
      if (!videoProfileData.data || !videoProfileData.data.questions) {
        throw new Error("No questions data found in fetched video profile.");
      }

      fetchedQuestions = JSON.parse(videoProfileData.data.questions);

      // Validate we have question data
      if (!fetchedQuestions.length) {
        toast.error("No interview data found. Please restart the interview.");
        setCurrentSection("deviceTesting");
        return;
      }

      console.log("Preparing video for upload");
      const file = new Blob(recordedChunks, {
        type: "video/webm;codecs=vp8,opus",
      });

      if (file.size === 0) {
        throw new Error("Generated video file is empty");
      }

      console.log("Video blob created:", {
        size: file.size,
        type: file.type,
        chunks: recordedChunks.length,
      });

      // Validate video file before upload
      const validation = validateVideoFile(file);
      if (!validation.valid) {
        throw new Error(`Invalid video file: ${validation.errors.join(', ')}`);
      }

      // Log upload method info
      const uploadInfo = getUploadMethodInfo(file.size);
      console.log('Upload method info:', uploadInfo);
      setUploadMethod(uploadInfo.method);

      setLoadingText("Preparing video upload...");
      setUploadProgress(0);

      // Use the new video upload service
      const finalUrl = await uploadVideo(file, vpid, {
        onProgress: (progressData) => {
          // Update progress state
          setUploadProgress(progressData.percentage);

          // Update loading text with progress information
          if (uploadInfo.method === 'multipart') {
            setLoadingText(`Uploading Video: ${Math.round(progressData.percentage)}% (${progressData.completedParts}/${progressData.totalParts} parts)`);
          } else {
            setLoadingText(`Uploading Video: ${Math.round(progressData.percentage)}%`);
          }
        },
        onError: (error) => {
          console.error('Upload error:', error);
          setUploadProgress(0);
        },
        setLoadingText: setLoadingText
      });

      // Upload successful
      setVideoUrl(finalUrl);
      console.log("Video URL set to:", finalUrl);
      uploadSuccess = true;

      // Generate Score Using LLM after successful upload
      console.log("Generating Score Now");
      setLoadingText("Getting Score from LLM");

      const Get_Score_Url = `${
        import.meta.env.VITE_APP_HOST
      }/api/v1/generateScore`;

      // Generate score
      let scoreResponse;
      try {
        const questionsToSend = fetchedQuestions
          .filter((_, index) => index !== 0)
          .map((q) => ({
            ...q,
            answer: q.answer || "No response recorded", // Ensure answer is not null
          }));

        console.log('Generating score for questions:', fetchedQuestions.length);
        console.log('Questions payload being sent:', JSON.stringify(questionsToSend, null, 2));

        scoreResponse = await fetch(Get_Score_Url, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            questions: questionsToSend,
          }),
        });

        if (!scoreResponse.ok) {
          const errorText = await scoreResponse.text();
          console.error('Score generation error response:', errorText);
          throw new Error(
            `Score generation failed: ${scoreResponse.status} ${scoreResponse.statusText}. Details: ${errorText}`
          );
        }

        scoreData = await scoreResponse.json();
        console.log("Raw score response:", scoreData);

        // Always set the score data even if it's default/invalid
        setScore(scoreData);

        if (scoreData.Suggestions?.includes("default score")) {
          console.warn("Using default score:", scoreData);
          toast("Using default scoring due to processing limitations", {
            icon: "⚠️",
          });
        } else if (
          !scoreData.score ||
          !scoreData.score.Skill_Score ||
          !scoreData.score.Communication_Score ||
          !scoreData.score.Overall_Score
        ) {
          console.warn(
            "Received potentially invalid score format:",
            scoreData
          );
          toast("Score format may be incorrect - using available data", {
            icon: "⚠️",
          });
        } else {
          console.log("Score processing completed successfully");
        }
      } catch (scoreError) {
        console.error("Score generation error:", scoreError);
        toast("Failed to generate score. Using default values.", {
          icon: "⚠️",
        });
        scoreData = {
          score: {
            Skill_Score: 5,
            Communication_Score: 5,
            Overall_Score: 5,
          },
          Suggestions: "Failed to generate report due to a technical issue. Please contact support.",
          error: scoreError.message, // Pass the error message from the backend
        };
        setScore(scoreData);
        // Error is already handled by toast notification above
      }

      // Validate and prepare data for API submission
      if (!finalUrl || !scoreData || !fetchedQuestions || !vpid) {
        throw new Error(
          "Missing required data. Need video URL, score, questions and profile ID."
        );
      }

      // Save to localStorage
      const storageData = {
        videoUrl: finalUrl,
        score: scoreData,
        status: scoreData.score.Overall_Score > 5 ? "active" : "inactive", // Score-based status
      };

      console.log("Saving to localStorage:", storageData);
      localStorage.setItem(
        `ReviewVideoProfile${vpid}`,
        JSON.stringify(storageData)
      );

      // Submit to API
      setLoadingText("Updating Video Profile Data...");
      const formData = {
        videoProfileId: vpid,
        videoUrl: finalUrl,
        score: scoreData, // Add score from generateScore API
        questions: fetchedQuestions
          .filter((_, index) => index !== 0)
          .map((q) => {
            const answer = q.answer?.trim();
            if (!answer) {
              console.warn(`Empty answer for question: ${q.question}`);
            }

            return {
              ...q,
              answer: answer || "No response recorded", // More accurate message
              status: "completed",
              isComplete: true,
              endTimestamp: new Date().toISOString(),
              scores: {
                    communication: answer ? 3 : 1, // Better default scores
                    technical: answer ? 3 : 1,
                    overall: answer ? 3 : 1,
                  },
                  metadata: {
                    wasAnswered: !!answer,
                    submittedAt: new Date().toISOString(),
                  },
                };
              }),
          };

          // Define the API URL
          const Finish_videoprofile_url = `${
            import.meta.env.VITE_APP_HOST
          }/api/v1/add-video-resume`;

          // Make an API call using fetch
          const response = await fetch(Finish_videoprofile_url, {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(formData),
          });

          // Check if the response is okay
          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(
              `HTTP error! status: ${response.status}. Message: ${
                errorData.message || "Unknown error"
              }`
            );
          }

      // Parse the JSON response
      const Apidata = await response.json();
      console.log("Success:", Apidata);
      localStorage.removeItem("formData");
      setCurrentSection("reviewVideoProfile");
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      const errorMessage = error.message || "An unknown error occurred";
      console.error("Error during interview ending:", errorMessage);

      // Handle different error scenarios
      if (
        error.message.includes("Failed to fetch") ||
        error.message.includes("Network")
      ) {
        // Network error
        toast("Network error - please check your connection", {
          icon: "🌐",
        });
      } else if (
        error.message.includes("score") ||
        error.message.includes("Score")
      ) {
        // Score generation error with uploaded video
        if (uploadSuccess) {
          toast("Using default scoring - processing limitations", {
            icon: "⚠️",
          });
          scoreData = {
            score: {
              Skill_Score: 5,
              Communication_Score: 5,
              Overall_Score: 5,
            },
            Suggestions: "Score generation failed. Using default scoring.",
          };
          setScore(scoreData);
          setCurrentSection("reviewVideoProfile");
          return;
        }
      } else {
        // Other errors
        toast("Interview processing encountered an error", {
          icon: "❌",
        });
      }

      // Final fallback handling
      if (uploadSuccess) {
        if (scoreData?.score) {
          console.log("Continuing with available score data despite error");
          setCurrentSection("reviewVideoProfile");
        } else {
          console.log("No score data available - using default");
          const defaultScore = {
            score: {
              Skill_Score: 5,
              Communication_Score: 5,
              Overall_Score: 5,
            },
            Suggestions: "Score unavailable. Using default values.",
          };
          setScore(defaultScore);
          setCurrentSection("reviewVideoProfile");
        }
      } else {
        console.log("Critical error - resetting to device testing");
        setCurrentSection("deviceTesting");
      }
    }
  }, [
    vpid,
    recordedChunks,
    stopRecording,
    stopAllStreams,
    exitFullScreen,
    setVideoUrl,
    setScore,
    setIsLoading,
    setLoadingText,
    setCurrentSection,
  ]);

  // Set the end interview callback for AI termination after handleEndInterview is defined
  useEffect(() => {
    if (setEndInterviewCallback && handleEndInterview) {
      setEndInterviewCallback(handleEndInterview);
    }
  }, [setEndInterviewCallback, handleEndInterview]);

  const finishVideoProfile = async () => {
    try {
      let scores = 0;

      if (score?.score?.Overall_Score !== undefined) {
        scores = score.score.Overall_Score;
      } else {
        console.warn("Using default score of 0 - score data may be incomplete");
        toast.warning("Using default overall score due to incomplete data");
      }

      // Get questions with timestamps from localStorage
      let questionsWithTimestamps = [];
      try {
        const qStr = localStorage.getItem("questions");
        if (qStr) {
          questionsWithTimestamps = JSON.parse(qStr);
        }
      } catch (e) {
        console.warn("Could not parse questions from localStorage", e);
      }

      const formData = {
        videoProfileId: vpid,
        score: scores,
        isDefaultScore: score?.Suggestions?.includes("default score") || false,
        questions: questionsWithTimestamps, // <-- include timepoints
      };

      // Define the API URL
      const Finish_videoprofile_url = `${
        import.meta.env.VITE_APP_HOST
      }/api/v1/finish-video-resume`;

      // Make an API call using fetch
      const response = await fetch(Finish_videoprofile_url, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json", // To Pass JSON if formData is a plain object
        },
        body: JSON.stringify(formData), // Send formData as JSON
      });
      const data = await response.json();
      console.log(data);
      setCurrentSection("endInterview");
      localStorage.removeItem(`ReviewVideoProfile${vpid}`);
    } catch (error) {
      const errorMessage = error.message || "An unknown error occurred";
      console.error("Error during profile completion:", errorMessage);
      toast.error(`Failed to complete profile: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchData = async () => {
    setIsLoading(true);
    try {
      // Try to get data from localStorage first
      const localDataStr = localStorage.getItem(`ReviewVideoProfile${vpid}`);
      if (localDataStr) {
        try {
          const localData = JSON.parse(localDataStr);
          if (
            localData?.score?.score &&
            typeof localData.score.score === "object"
          ) {
            setScore(localData.score);
            setVideoUrl(localData.videoUrl);
            setStatus(localData.status);
            setCurrentSection("reviewVideoProfile");
            return;
          }
        } catch (e) {
          console.error("Error parsing localStorage data:", e);
        }
      }

      // If no valid localStorage data, fetch from API
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/video-resume-data/${vpid}`,
        {
          method: "GET",
        }
      );

      const res = await response.json();

      if (!response.ok) {
        throw new Error(res.message || "Failed to fetch video resume data");
      }

      if (!res.data) {
        throw new Error("No data received from server");
      }

      if (res.data.score && res.data.video_url) {
        try {
          const parsedScore = JSON.parse(res.data.score);
          if (!parsedScore.score || typeof parsedScore.score !== "object") {
            throw new Error("Invalid score format in API response");
          }
          setScore(parsedScore);
          setVideoUrl(res.data.video_url);
          setStatus(res.data.status);
          setCurrentSection("reviewVideoProfile");
        } catch (error) {
          console.error("Error parsing score from API:", error);
          setScore({
            score: {
              Communication_Score: 0,
              Skill_Score: 0,
              Overall_Score: 0,
            },
            Suggestions: "Score data unavailable or invalid",
          });
          setVideoUrl(res.data.video_url);
          setStatus(res.data.status);
          setCurrentSection("reviewVideoProfile");
        }
      } else if (res.data.questions) {
        try {
          const parsedQuestions = JSON.parse(res.data.questions);
          console.log("Parsed API response:", {
            rawQuestions: res.data.questions,
            parsedQuestions: parsedQuestions,
          });

          if (!Array.isArray(parsedQuestions) || parsedQuestions.length === 0) {
            throw new Error("No valid questions found");
          }

          // Only use the first question from the API
          const firstApiQuestion = parsedQuestions[0];
          console.log(
            "About to initialize with first question:",
            firstApiQuestion
          );
          const success = await initializeQuestions([firstApiQuestion]);
          console.log("Initialization result:", {
            success,
            currentQuestions: questions,
            currentIndex,
          });

          if (!success) {
            throw new Error("Failed to initialize questions");
          }

          console.log("Question initialized successfully:", {
            firstQuestion: firstApiQuestion?.question,
          });

          setCurrentSection("deviceTesting");
        } catch (parseError) {
          console.error("Error initializing questions:", parseError);
          throw new Error("Failed to setup interview questions");
        }
      } else {
        throw new Error("Invalid data format received from server");
      }
    } catch (error) {
      console.error("Error fetching data:", error);
      toast.error(error.message || "Something went wrong");
      navigate("/");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    if (currentSection === "deviceTesting" && !questionsLoaded && !isLoading) {
      loadQuestionsData().catch(() => {});
    }
  }, [currentSection, questionsLoaded, isLoading]);

  // Removed beforeunload handler to prevent duplicate browser warning
  // useEffect(() => {
  //   const handleBeforeUnload = (event) => {
  //     if (isInterviewActive) {
  //       event.preventDefault();
  //       event.returnValue = "Are you sure you want to leave? Your progress will be lost.";
  //     }
  //   };
  //
  //   window.addEventListener("beforeunload", handleBeforeUnload);
  //
  //   return () => {
  //     window.removeEventListener("beforeunload", handleBeforeUnload);
  //   };
  // }, [isInterviewActive]);

  const handleBackNavigation = () => {
    if (isInterviewActive) {
      if (window.confirm("Are you sure you want to go back? Your interview progress will be lost.")) {
        navigate(-1);
      }
    } else {
      navigate(-1);
    }
  };

  // --- End handlers/effects ---

  return (
    <DeviceTestContent
      isLoading={isLoading}
      loadingText={loadingText}
      currentSection={currentSection}
      localCamStream={localCamStream}
      startWebcam={startWebcam}
      handleStartInterview={handleStartInterview}
      handleAcceptTerms={handleAcceptTerms}
      onBack={handleBackNavigation}
      questions={questions}
      currentQuestion={currentQuestion}
      currentIndex={currentIndex}
      error={error}
      questionsLoading={questionsLoading}
      updateAnswer={updateAnswer}
      isInterviewActive={isInterviewActive}
      nextQuestion={nextQuestion}
      handleEndInterview={handleEndInterview}
      startAnswering={startAnswering}
      status={status}
      finishVideoProfile={finishVideoProfile}
      videoUrl={videoUrl}
      score={score}
      termsAcceptedAndDismissed={termsAcceptedAndDismissed}
      setTermsAcceptedAndDismissed={setTermsAcceptedAndDismissed}
      addPollyAudioToMix={addPollyAudioToMix}
      // Phase 1: Removed requirement status props to simplify interview flow
    />
  );
}
